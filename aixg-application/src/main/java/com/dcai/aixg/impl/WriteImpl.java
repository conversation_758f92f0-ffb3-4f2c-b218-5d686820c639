package com.dcai.aixg.impl;

import static com.dcai.aixg.dto.FlowDTO.SrcType.TASK;
import static com.dcai.aixg.dto.FlowDTO.SrcType.TWEET;
import static com.ejuetc.commons.base.querydomain.impl.Domain2DTOConvert.convert2DTO;
import static com.ejuetc.commons.base.response.ApiResponse.succ;
import static com.ejuetc.commons.base.spring.SpringUtil.getBean;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dcai.aixg.api.WriteAPI;
import com.dcai.aixg.domain.aiagent.flow.Flow;
import com.dcai.aixg.domain.aiagent.flow.FlowRpt;
import com.dcai.aixg.domain.aiagent.flow.FlowService;
import com.dcai.aixg.domain.broker.Broker;
import com.dcai.aixg.domain.broker.BrokerRpt;
import com.dcai.aixg.domain.task.Policy;
import com.dcai.aixg.domain.task.PolicyRpt;
import com.dcai.aixg.domain.task.Report;
import com.dcai.aixg.domain.task.Task;
import com.dcai.aixg.domain.task.TaskRpt;
import com.dcai.aixg.domain.task.Write;
import com.dcai.aixg.dto.BrokerDTO;
import com.dcai.aixg.dto.FlowDTO;
import com.dcai.aixg.dto.NodeDTO;
import com.dcai.aixg.dto.task.TaskDTO;
import com.dcai.aixg.dto.task.WriteCreateDTO;
import com.dcai.aixg.dto.task.WriteDetailDTO;
import com.dcai.aixg.dto.task.WriteInfoDTO;
import com.dcai.aixg.dto.task.WriteShareDTO;
import com.dcai.aixg.integration.wechat.WechatService;
import com.dcai.aixg.kerApi.KerApi;
import com.dcai.aixg.pro.LaunchFlowPO;
import com.dcai.aixg.pro.search.ListQueryPO;
import com.dcai.aixg.pro.task.WriteCreatePO;
import com.dcai.aixg.pro.task.WriteSharePO;
import com.ejuetc.commons.base.exception.BusinessException;
import com.ejuetc.commons.base.filter.login.SaasLoginToken;
import com.ejuetc.commons.base.response.ApiResponse;

import jakarta.persistence.criteria.Predicate;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RefreshScope
@RequiredArgsConstructor
@Slf4j
@RestController
@Transactional(rollbackFor = Exception.class)
public class WriteImpl implements WriteAPI {

    private final BrokerRpt brokerRpt;
    private final TaskRpt taskRpt;
    private final FlowRpt flowRpt;
    private final PolicyRpt policyRpt;
    private final KerApi kerApi;

    @Autowired
    private FlowService flowService;

    @Override
    public ApiResponse<List<String>> policyList(SaasLoginToken saasLoginToken, String cityName) {
        Broker broker = brokerRpt.findById(saasLoginToken.getUserId()).orElseThrow(() -> new BusinessException("bc.cpm.aixg.1002"));
        List<Policy> policyList = policyRpt.findByCityName(cityName);
        return succ(policyList.stream().map(Policy::getTitle).collect(Collectors.toList()));
    }

    @Override
    public ApiResponse<WriteCreateDTO> doWrite(SaasLoginToken saasLoginToken, @Valid WriteCreatePO po) {
        po.checkParams();
        Broker broker = brokerRpt.findById(saasLoginToken.getUserId()).orElseThrow(() -> new BusinessException("bc.cpm.aixg.1002"));
        po.setUserId(broker.getKerId());
        Write write = new Write(broker, po);
        write = createWrite(write);
        LaunchFlowPO launchFlowPO = new LaunchFlowPO()
                .setSrcType(TASK)
                .setSrcId(write.getId())
                .setConfigCode(po.getConfigCode())
                .setRequest(po.toMap())
                .setRunMode(po.getRunMode());
        ApiResponse<FlowDTO> flowResp = flowService.launch(launchFlowPO);
        if (flowResp == null || !flowResp.isSucc()) throw new BusinessException("bc.cpm.aixg.1002");
        FlowDTO flowDTO = flowResp.getData();
        write.setFlowId(flowDTO.getId());
        //Flow flow = flowRpt.getReferenceById(flowDTO.getId());
        //Generate generate = flow.getGenerates().get(0);
        //NodeDTO currentNode1 = flowDTO.getCurrentNode();
        //NodeDTO currentNode = flowDTO.getNodes().get(0);
        //GenerateDTO curGenerateDTO = flowDTO.getGenerates().get(flowDTO.getGenerates().size() - 1);
        WriteCreateDTO dto = new WriteCreateDTO()
        		.setTaskId(write.getId())
        		//.setWriteId(currentNode.getProcessInfo2())
        		//.setArticleId(currentNode.getProcessInfo3())
        		;
        if (StringUtils.isBlank(broker.getMessageOpenId())) {
            String qrCodeUrl = getBean(WechatService.class).getQrCodeTicket(write.getId());
            dto.setQrCodeUrl(qrCodeUrl);
        } else {
            write.setOpenId(broker.getMessageOpenId());
            dto.setFollowStatus(true);
        }
        return succ(dto);
    }

    @Transactional(isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRES_NEW)
    protected Write createWrite(Write write) {
        return taskRpt.save(write);
    }

    @Override
    public ApiResponse<WriteCreateDTO> doWriteByTaskId(Long taskId, Long flowId, WriteCreatePO po) {
        log.info("WriteImpl.doWriteByTaskId, taskId={}, flowId={}, po={}", taskId, flowId, JSONObject.toJSONString(po));
        po.checkParams();
        try {
        	Thread.sleep(1000L);
        } catch (InterruptedException e) {
        	e.printStackTrace();
        }
        Write write = taskRpt.findByTaskId(taskId);
        if (write == null) throw new BusinessException("bc.cpm.aixg.1006");
        WriteCreateDTO dto = kerApi.doWrite(write.getBroker().getKerId(), po);
        write.writeSubmit(dto, flowId);
        return succ(dto);
    }

	@Override
	public ApiResponse<WriteCreateDTO> doWrite(Long kerId, WriteCreatePO po) {
		log.info("WriteImpl.doWrite, kerId={}, po={}", kerId, JSONObject.toJSONString(po));
		WriteCreateDTO dto = kerApi.doWrite(kerId, po);
		return succ(dto);
	}

    @Override
    public ApiResponse<List<WriteDetailDTO>> libraryWriteList(SaasLoginToken saasLoginToken, @Valid ListQueryPO po) {
    	Broker broker = brokerRpt.findById(saasLoginToken.getUserId()).orElseThrow(() -> new BusinessException("bc.cpm.aixg.1002"));
    	Page<Task> task = findWriteList(broker, po);
    	List<WriteDetailDTO> result = task.stream().map(t -> t.makeWriteDetailDTO()).collect(Collectors.toList());
    	return succ(result);
    }
    
    private Page<Task> findWriteList(Broker broker, ListQueryPO po) {
    	Pageable pageable = PageRequest.of(po.getPage(), po.getPageSize());
        Sort sort = Sort.by(Sort.Direction.DESC, "createTime");
        Specification<Task> specification = (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(cb.equal(root.get("broker"), broker));
            if (StringUtils.isNotBlank(po.getWriteType())) {
                predicates.add(cb.equal(root.get("writeType"), po.getWriteType()));
            }
            if (StringUtils.isNotBlank(po.getSubType())){
                predicates.add(cb.equal(root.get("subType"), po.getSubType()));
            }
            if (po.getStartDate() != null) {
                predicates.add(cb.greaterThanOrEqualTo(root.get("createTime"), po.getStartDate()));
            }
            if (po.getEndDate() != null) {
                predicates.add(cb.lessThanOrEqualTo(root.get("createTime"), po.getEndDate()));
            }
            if (po.getStatus() != null) {
            	predicates.add(cb.equal(root.get("status"), po.getStatus()));
            }
            return query.where(predicates.toArray(new Predicate[predicates.size()])).getRestriction();
        };
    	return taskRpt.findAll(specification, pageable);
	}

    @Override
    public ApiResponse<List<WriteDetailDTO>> writeList(SaasLoginToken saasLoginToken, @Valid ListQueryPO po) {
        Broker broker = brokerRpt.findById(saasLoginToken.getUserId()).orElseThrow(() -> new BusinessException("bc.cpm.aixg.1002"));
        return kerApi.writeList(broker.getKerId(), po);
    }

    @Override
    public ApiResponse<WriteDetailDTO> writeDetail(SaasLoginToken saasLoginToken, String writeId) {
        Broker broker = brokerRpt.findById(saasLoginToken.getUserId()).orElseThrow(() -> new BusinessException("bc.cpm.aixg.1002"));
        WriteDetailDTO result = kerApi.writeDetail(broker.getKerId(), writeId);
        if (result != null && result.getTaskStatus().equals("DONE")) {
            if (broker.getCurrentlyValid()) {
                result.setBroker(convert2DTO(broker, new BrokerDTO()));
            }
            if (StringUtils.isNotBlank(result.getContent())) {
                List<Write> writeList = taskRpt.findAndLockByWriteId(writeId);
                if (writeList != null && writeList.size() > 0) {
                    Write write = writeList.get(0);
                    List houseInfo = (write.getHouseInfos() != null && write.getHouseInfos().size() > 0) ? write.getHouseInfos4DelegationVO() : write.getDelegationIds(result.getContent());
                    result.setHouseInfo(houseInfo);
                } else {
                    result.setHouseInfo(new Write().getDelegationIds(result.getContent()));
                }
            }
        }
        return succ(result);
    }

    @Override
    public ApiResponse<WriteDetailDTO> writeDetail4Share(String writeId) {
        List<Write> writeList = taskRpt.findAndLockByWriteId(writeId);
        if (writeList == null || writeList.size() == 0) throw new BusinessException("bc.cpm.aixg.1006");
        Write write = writeList.get(0);
        WriteDetailDTO result = kerApi.writeDetail(write.getBroker().getKerId(), writeId);
        if (result != null && result.getTaskStatus().equals("DONE")) {
            if (write.getBroker().getCurrentlyValid()) {
                result.setBroker(convert2DTO(write.getBroker(), new BrokerDTO()));
            }
            if (StringUtils.isNotBlank(result.getContent())) {
                List houseInfo = (write.getHouseInfos() != null && write.getHouseInfos().size() > 0) ? write.getHouseInfos4DelegationVO() : write.getDelegationIds(result.getContent());
                result.setHouseInfo(houseInfo);
            }
        }
        return succ(result);
    }

    @Override
    public ApiResponse<WriteInfoDTO> writeInfo(SaasLoginToken saasLoginToken) {
        return succ(new WriteInfoDTO());
    }

    @Override
    public ApiResponse<Boolean> deleteWrite(SaasLoginToken saasLoginToken, String writeId) {
        Broker broker = brokerRpt.findById(saasLoginToken.getUserId()).orElseThrow(() -> new BusinessException("bc.cpm.aixg.1002"));
        List<Write> writeList = taskRpt.findAndLockByWriteId(writeId);
        if (writeList != null && writeList.size() > 0) {
            Write write = writeList.get(0);
            if (!broker.getId().equals(write.getBroker().getId())) throw new BusinessException("bc.cpm.aixg.1005");
            write.setLogicDelete();
            boolean result = kerApi.writeDelete(broker.getKerId(), writeId);
            if (!result) return succ(Boolean.FALSE);
        }
        return succ(Boolean.TRUE);
    }

    @Override
    public ApiResponse<WriteShareDTO> createWriteShare(SaasLoginToken saasLoginToken, WriteSharePO po) {
        Broker broker = brokerRpt.findById(saasLoginToken.getUserId()).orElseThrow(() -> new BusinessException("bc.cpm.aixg.1002"));
        Write write = (Write) taskRpt.findAndLockById(po.getTaskId());
        if (write == null) throw new BusinessException("bc.cpm.aixg.1006");
        if (write.getSubType() != TaskDTO.SubType.ORIGIN) throw new BusinessException("bc.cpm.aixg.1014", "仅支持对原始文章进行分享操作");
        Write shareWrite = new Write(write, po.getSubType());
        shareWrite = createWrite(shareWrite);
        LaunchFlowPO launchFlowPO = new LaunchFlowPO()
                .setSrcType(TWEET)
                .setSrcId(shareWrite.getId())
                .setConfigCode((shareWrite.getSubType() == TaskDTO.SubType.REDNOTE) ? "SHARE_REDNOTE_FLOW" : "SHARE_WECHAT_FLOW")
                .setRequest(JSON.parseObject(write.getContent()))
                .setRunMode(LaunchFlowPO.RunMode.SYNC);
        ApiResponse<FlowDTO> flowResponse = flowService.launch(launchFlowPO);
        WriteShareDTO result = new WriteShareDTO();
        result.setContent(flowResponse.getData().getResponse());
        result.setChapterImages(shareWrite.generatorImage(po.getChapters()));
        return succ(result);
    }

    @Override
    public ApiResponse<String> changeFollow(SaasLoginToken saasLoginToken, String writeId) {
        List<Write> writeList = taskRpt.findAndLockByWriteId(writeId);
        if (writeList != null && writeList.isEmpty()) {
            Write write = writeList.get(0);
            write.setOpenId(null);
            String qrCodeUrl = getBean(WechatService.class).getQrCodeTicket(write.getId());
            return succ(qrCodeUrl);
        }
        List<Report> reportList = taskRpt.findAndLockByReportId(writeId);
        if (reportList != null && reportList.size() > 0) {
            Report report = reportList.get(0);
            report.setOpenId(null);
            String qrCodeUrl = getBean(WechatService.class).getQrCodeTicket(report.getId());
            return succ(qrCodeUrl);
        }
        throw new BusinessException("bc.cpm.aixg.1006");
    }

	@Override
	public ApiResponse<WriteCreateDTO> createWrite4BrokerByFlowId(Long brokerId, Long flowId) {
		Broker broker = brokerRpt.findById(brokerId).orElseThrow(() -> new BusinessException("bc.cpm.aixg.1002"));
		Flow flow = flowRpt.findById(flowId).orElseThrow(() -> new BusinessException("bc.cpm.aixg.1002"));
		Write write = new Write(broker, flow);
        write = createWrite(write);
        WriteCreateDTO dto = new WriteCreateDTO().setTaskId(write.getId());
		return succ(dto);
	}

	@Override
	public ApiResponse<List<String>> lifeInfos(SaasLoginToken saasLoginToken) {
		Broker broker = brokerRpt.findById(saasLoginToken.getUserId()).orElseThrow(() -> new BusinessException("bc.cpm.aixg.1002"));
		List<String> result = Arrays.asList("商场开业", "招商引资", "地铁开挖", "道路拓宽", "学校新建", "办学政策改变", "动迁动态", "新建医院", "名医/名师常驻", "其他");
		return succ(result);
	}

	@Override
	public ApiResponse<WriteCreateDTO> notifyWriteResult(Long taskId, FlowDTO.Status status, String response) {
		Write write = taskRpt.findByTaskId(taskId);
		write.handleCallBack4Create(status);
		if (write.getStatus() == TaskDTO.Status.DONE) {
			JSONObject jsonObject = JSONObject.parseObject(response);
			write.buildTaskDetail(jsonObject.getString("title"), null, response, LocalDateTime.now());
			write.handleSendMessage();
		}
		return succ();
	}

}
