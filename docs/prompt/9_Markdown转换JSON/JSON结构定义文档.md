
## 核心数据结构

### 1. DocumentData（最终输出结构）

```json
{
  "type": "MONTHLY_REPORT|ANALYSIS_REPORT|MARKET_REPORT|...",
  "title": "文档标题",
  "subtitle": "副标题（可选）",
  "widgets": [
    // 控件数组，按serial编号排序
  ]
}
```

**字段说明**：
- `type`: 文档类型（必需）
- `title`: 文档主标题（必需）
- `subtitle`: 文档副标题（可选）
- `widgets`: 控件数组（必需），包含所有处理后的控件

### 2. 控件基础结构

所有控件都包含以下基础字段：

```json
{
  "serial": "序列编号",
  "type": "控件类型",
  "style": "样式类型",
  "title": "控件标题（可选）"
}
```

**字段约束**：
- `serial`: 字符串，层级编号（如"1", "1.1", "1.1.1"）
- `type`: 枚举值，见控件类型定义
- `style`: 枚举值，根据控件类型确定可选值
- `title`: 字符串，可选，移除加粗标记

## 控件类型定义

### 1. TITLE控件

**用途**：文档标题和章节标题

```json
{
  "serial": "1",
  "type": "TITLE",
  "style": "DOCUMENT|SECTION|PARAGRAPH|ENTRY",
  "title": "标题内容"
}
```

**样式说明**：
- `DOCUMENT`: 文档主标题（编号固定为"0"）
- `SECTION`: 章节标题（编号为"1", "2", "3"等）
- `PARAGRAPH`: 段落标题（编号为"1.1", "1.2"等）
- `ENTRY`: 条目标题（编号为"1.1.1"等）

**生成规则**：
- Markdown标题语法（#, ##, ###, ####）强制推荐为TITLE控件
- 优先级高于所有其他控件类型推荐

### 2. TEXT控件

**用途**：段落文本和分析性内容

```json
{
  "serial": "1.1",
  "type": "TEXT",
  "style": "FLOAT|BOARD|EMPHASIS|PLAIN",
  "title": "标题（可选）",
  "content": "文本内容"
}
```

**样式说明**：
- `FLOAT`: 浮动文本（引言、摘要、前言等）
- `BOARD`: 重点突出（分析性内容、数据解读、趋势分析）
- `EMPHASIS`: 强调文本（重要结论、核心要点）
- `PLAIN`: 普通文本（一般描述性内容）

**字段处理**：
- `title`: 移除加粗标记
- `content`: 保留加粗标记（用于前端特殊强化显示）

### 3. LIST控件

**用途**：列表结构内容

```json
{
  "serial": "1.2",
  "type": "LIST",
  "style": "SERIAL|BULLET|BOARD",
  "title": "列表标题（智能处理）",
  "content": [
    {
      "title": "项目标题",
      "content": "项目内容"
    }
  ]
}
```

**样式说明**：
- `SERIAL`: 有序列表（数字编号列表）
- `BULLET`: 无序列表（符号列表）
- `BOARD`: 重点列表（分析要点、核心优势等）

**字段处理规则**：
- 检测冒号分隔符（`**标题**：`或`标题：`）
- 冒号前内容提取到title字段（移除加粗标记）
- 冒号后内容放入content字段
- content字段保留加粗标记

### 4. TABLE控件

**用途**：表格数据展示

```json
{
  "serial": "2.1",
  "type": "TABLE",
  "style": "NORMAL|BOARD",
  "title": "表格标题",
  "cols": ["列1", "列2", "列3"],
  "content": [
    [
      {"type": "TEXT", "content": "内容1"},
      {"type": "TEXT", "content": "内容2", "recommended": true},
      {"type": "CHANGE", "content": "±XX%"}
    ]
  ]
}
```

**样式说明**：
- `NORMAL`: 普通数据表格（多行数据）
- `BOARD`: 重要数据面板（单行关键数据）

**TableCell类型**：
- `TEXT`: 文本内容（最常用）
- `IMAGE`: 图片URL
- `PROGRESS_BAR`: 进度值(0-100的数字)
- `CHANGE`: 涨跌幅数据

**recommended属性**：
- 用于标记具有明显优势的数据项
- 仅在对比性表格中使用
- 推荐项不超过总数据项的30%

### 5. CHART控件

**用途**：图表数据可视化

#### PIE图格式
```json
{
  "serial": "2.1",
  "type": "CHART",
  "style": "PIE",
  "title": "数据分布（单位说明）",
  "content": [
    {
      "title": "分类名称",
      "content": 数值
    }
  ]
}
```

#### BAR/LINE图格式
```json
{
  "serial": "2.2",
  "type": "CHART",
  "style": "BAR|LINE",
  "title": "数据对比（单位说明）",
  "cols": ["X轴标签1", "X轴标签2", "X轴标签3"],
  "content": [
    {
      "title": "数据系列名称",
      "content": [数值1, 数值2, 数值3]
    }
  ]
}
```

**重要结构说明**：
- **cols数组**：表示X轴标签（如时间点、分类名称）
- **content[].title**：表示数据系列名称（如指标名称）
- **content[].content**：表示对应的数值数组，长度必须与cols数组长度一致
- **LINE图特殊要求**：cols通常是时间轴，content[].title是数据指标名称

**样式说明**：
- `PIE`: 饼图（占比数据、分布数据）
- `BAR`: 柱状图（对比数据、分类数据）
- `LINE`: 折线图（趋势数据、时间序列）

**CHART控件结构要求**：
- **BAR/LINE图必须包含cols字段**
- **cols数组长度必须等于content中每个数据系列的数值数量**
- **LINE图特殊结构**：
  - cols: 时间轴标签（如["2024年08月", "2024年09月", ...]）
  - content[].title: 数据指标名称（如"挂牌均价"）
  - content[].content: 对应时间点的数值数组
- **BAR图结构**：
  - cols: 数据分类或指标名称
  - content[].title: 分类项名称
  - content[].content: 对应的数值数组

**数值处理规则**：
- 数值≥10000时转换为万单位（除以10000）
- 转换后保持数字类型，不包含"万"字符
- 单位信息在标题中标注
- 同一图表内数值单位必须一致

**图表优先原则**：
- 当表格数据包含数值且适合可视化展示时，优先转换为CHART控件
- 图表比表格能提供更好的数据可视化效果
- 仅当数据不适合图表展示时才使用TABLE控件

### 6. CARD控件

**用途**：结构化信息卡片

#### 基础结构
```json
{
  "serial": "3.1",
  "type": "CARD",
  "style": "BROKER|HOUSING|COMMUNITY",
  "title": "卡片标题",
  "fields": {
    // 根据样式类型确定具体字段
  }
}
```

#### BROKER卡片（经纪人）
```json
{
  "style": "BROKER",
  "fields": {
    "name": "姓名",
    "education": "学历",
    "experience": "服务年限",
    "serviceCategory": [
      "服务类别1",
      "服务类别2"
    ],
    "specialSkill": [
      "特色专长1",
      "特色专长1"
    ],
    "suggest": "投资建议",
    "wechat": "微信图片url",
    "phone": "联系电话"
  }
}
```

#### HOUSING卡片（房源）
```json
{
  "style": "HOUSING",
  "fields": {
    "layout": "户型",
    "area": "建筑面积",
    "floor": "楼层信息",
    "orientation": "朝向",
    "decoration": "装修状况",
    "totalPrice": "总价",
    "unitPrice": "单价",
    "propertyType": "房产类型"
  }
}
```

#### COMMUNITY卡片（小区）
```json
{
  "style": "COMMUNITY",
  "fields": {
    "buildYear": "建筑年代",
    "propertyCompany": "物业公司",
    "propertyFee": "物业费",
    "greenRate": "绿化率",
    "plotRatio": "容积率",
    "parkingSpaces": "停车位信息",
    "facilities": "主要配套设施"
  }
}
```

## 序列编号规则

### 编号层次结构
- **0级**: 文档标题（编号固定为"0"）
- **1级**: 章节级内容（编号为"1", "2", "3"等）
- **1.1级**: 段落级内容（编号为"1.1", "1.2"等）
- **1.1.1级**: 条目级内容（编号为"1.1.1"等）

### 编号分配原则
1. **连续递增**: 同级编号必须连续，不得跳跃
2. **层级对应**: 编号深度与内容层级严格对应
3. **全局预分配**: Step2中为所有控件预分配编号
4. **顺序一致性**: segment_id顺序与serial编号逻辑顺序一致
5. **重复标题避免**: 父子级控件标题不应重复，子级控件可省略title字段或使用不同标题

## 数据约束和验证规则

### 必需字段验证
- DocumentData: `type`, `title`, `widgets`
- 所有控件: `serial`, `type`
- TITLE控件: `title`
- TEXT控件: `content`
- LIST控件: `content`（数组格式）
- TABLE控件: `cols`, `content`
- CHART控件: `content`，BAR/LINE图还需`cols`
- CARD控件: `name`, `fields`

### 数据类型约束
- `serial`: 字符串，符合层级编号格式
- 数值字段: 纯数字类型，不包含单位文字
- 数组字段: 必须为数组类型
- 枚举字段: 必须为预定义的枚举值

### 格式规范
- 加粗标记处理: title字段移除，content字段保留
- 冒号分隔处理: 必须分离到title和content字段
- 单位转换: 万单位转换规则和标注要求
- JSON转义: 正确处理特殊字符转义

### 质量控制规范
- **图表优先验证**: 数值型表格数据应转换为CHART控件而非TABLE控件
- **重复标题检查**: 父子级控件不应有相同标题，避免界面重复显示
- **控件类型选择**: 根据数据特征选择最适合的控件类型和样式
- **CHART结构验证**:
  - LINE图中cols必须是时间轴，content[].title必须是指标名称
  - BAR图中cols是分类或指标，content[].title是分类项名称
  - content[].content数组长度必须与cols数组长度一致
  - 所有数值必须为数字类型，不能包含单位文字
